#[cfg(test)]
mod optimization_tests {
    use super::super::*;
    use crate::config::{Config<PERSON>anager, Settings};
    use crate::download::resume::ResumeManagerImpl;
    use crate::storage::storage_impl::LocalStorage;
    use std::sync::Arc;
    use uuid::Uuid;
    use tokio::fs;

    async fn create_test_downloader_with_optimizations() -> HttpDownloader {
        let mut settings = Settings::default();
        settings.download.max_concurrent_chunks = Some(4);
        settings.download.buffer_size = 8 * 1024 * 1024; // 8MB
        
        let config_manager = Arc::new(ConfigManager::with_settings(settings.clone()));
        let storage = Arc::new(LocalStorage::new(settings.download.path.clone()));
        let resume_manager = Arc::new(ResumeManagerImpl::new(settings, storage));
        
        let task_id = Uuid::new_v4();
        let url = "https://httpbin.org/bytes/1048576".to_string(); // 1MB test file
        let output_path = "test_optimized_download.bin".to_string();
        
        HttpDownloader::new(url, output_path, config_manager, task_id)
            .with_resume_manager(resume_manager)
            .with_multi_thread(true, Some(4))
            .with_checksum_verification(true, Some(vec![
                "sha256".to_string(),
                "md5".to_string(),
            ]))
            .with_memory_management(128, Some(0.8)) // 128MB limit, 80% threshold
    }

    #[tokio::test]
    async fn test_checksum_calculator() {
        use super::super::checksum_manager::{ChecksumCalculator, HashAlgorithm};
        use std::collections::HashMap;
        
        // 创建测试文件
        let test_data = b"Hello, World! This is a test file for checksum calculation.";
        let test_file_path = "test_checksum_file.txt";
        fs::write(test_file_path, test_data).await.unwrap();
        
        // 创建校验和计算器
        let calculator = ChecksumCalculator::new(1024)
            .with_algorithms(vec![
                HashAlgorithm::Md5,
                HashAlgorithm::Sha256,
                HashAlgorithm::Blake3,
            ]);
        
        // 计算校验和
        let checksums = calculator.calculate_file_checksums(std::path::Path::new(test_file_path)).await.unwrap();
        
        // 验证结果
        assert!(checksums.contains_key(&HashAlgorithm::Md5));
        assert!(checksums.contains_key(&HashAlgorithm::Sha256));
        assert!(checksums.contains_key(&HashAlgorithm::Blake3));
        
        // 验证校验和长度
        assert_eq!(checksums[&HashAlgorithm::Md5].len(), 32); // MD5 is 32 hex chars
        assert_eq!(checksums[&HashAlgorithm::Sha256].len(), 64); // SHA256 is 64 hex chars
        assert_eq!(checksums[&HashAlgorithm::Blake3].len(), 64); // BLAKE3 is 64 hex chars
        
        // 测试校验和验证
        let mut expected_checksums = HashMap::new();
        expected_checksums.insert(HashAlgorithm::Sha256, checksums[&HashAlgorithm::Sha256].clone());
        
        let verification_results = calculator.verify_file_checksums(
            std::path::Path::new(test_file_path), 
            &expected_checksums
        ).await.unwrap();
        
        assert_eq!(verification_results[&HashAlgorithm::Sha256], true);
        
        // 清理
        let _ = fs::remove_file(test_file_path).await;
    }

    #[tokio::test]
    async fn test_chunk_checksum_calculation() {
        use super::super::checksum_manager::{ChecksumCalculator, HashAlgorithm};
        
        // 创建测试文件
        let test_data = b"0123456789".repeat(100); // 1000 bytes
        let test_file_path = "test_chunk_checksum_file.txt";
        fs::write(test_file_path, &test_data).await.unwrap();
        
        let calculator = ChecksumCalculator::new(512)
            .with_algorithms(vec![HashAlgorithm::Sha256]);
        
        // 计算前500字节的校验和
        let chunk_checksums = calculator.calculate_chunk_checksums(
            std::path::Path::new(test_file_path), 
            0, 
            499
        ).await.unwrap();
        
        // 计算整个文件的校验和
        let file_checksums = calculator.calculate_file_checksums(
            std::path::Path::new(test_file_path)
        ).await.unwrap();
        
        // 分片校验和应该与整个文件的校验和不同
        assert_ne!(
            chunk_checksums[&HashAlgorithm::Sha256], 
            file_checksums[&HashAlgorithm::Sha256]
        );
        
        // 清理
        let _ = fs::remove_file(test_file_path).await;
    }

    #[tokio::test]
    async fn test_io_optimizer() {
        use super::super::io_optimizer::{OptimizedFileWriter, MemoryMonitor};
        use tokio::fs::File;
        
        let test_file_path = "test_io_optimizer.bin";
        let file = File::create(test_file_path).await.unwrap();
        
        // 创建优化的文件写入器
        let mut writer = OptimizedFileWriter::new(
            file,
            4 * 1024, // 4KB buffer
            std::time::Duration::from_millis(100),
        ).await.unwrap();
        
        // 写入测试数据
        let test_data = vec![0u8; 1024]; // 1KB
        for _ in 0..5 {
            writer.write_optimized(&test_data).await.unwrap();
        }
        
        writer.flush().await.unwrap();
        
        // 获取统计信息
        let stats = writer.get_stats().await;
        assert!(stats.total_writes > 0);
        assert!(stats.total_bytes_written >= 5 * 1024);
        assert!(stats.total_flushes > 0);
        
        // 清理
        let _ = fs::remove_file(test_file_path).await;
    }

    #[tokio::test]
    async fn test_memory_monitor() {
        use super::super::io_optimizer::MemoryMonitor;
        
        let mut monitor = MemoryMonitor::new(10); // 10MB limit
        
        // 测试内存分配
        assert!(monitor.can_allocate(5 * 1024 * 1024).await); // 5MB should be OK
        monitor.allocate(5 * 1024 * 1024).await.unwrap();
        
        // 检查使用率
        let usage_ratio = monitor.get_usage_ratio().await;
        assert!(usage_ratio > 0.4 && usage_ratio < 0.6); // Should be around 50%
        
        // 测试超出限制的分配
        assert!(!monitor.can_allocate(6 * 1024 * 1024).await); // 6MB would exceed limit
        
        // 释放内存
        monitor.deallocate(2 * 1024 * 1024).await;
        let new_usage_ratio = monitor.get_usage_ratio().await;
        assert!(new_usage_ratio < usage_ratio);
    }

    #[tokio::test]
    async fn test_smart_buffer_management() {
        let mut downloader = create_test_downloader_with_optimizations().await;
        
        let initial_buffer_size = downloader.buffer_size;
        
        // 模拟高速网络条件
        let high_speed = 50 * 1024 * 1024; // 50MB/s
        let low_latency = std::time::Duration::from_millis(20);
        
        downloader.smart_buffer_management(high_speed, low_latency).await.unwrap();
        
        // 高速网络应该增加缓冲区大小
        assert!(downloader.buffer_size >= initial_buffer_size);
        
        // 模拟低速网络条件
        let low_speed = 100 * 1024; // 100KB/s
        let high_latency = std::time::Duration::from_millis(500);
        
        downloader.smart_buffer_management(low_speed, high_latency).await.unwrap();
        
        // 低速网络应该使用较小的缓冲区
        assert!(downloader.buffer_size <= 8 * 1024 * 1024); // Should not exceed 8MB for low speed
    }

    #[tokio::test]
    async fn test_buffer_stats() {
        let downloader = create_test_downloader_with_optimizations().await;
        let stats = downloader.get_buffer_stats();
        
        assert!(stats.buffer_size > 0);
        assert!(stats.usage_ratio >= 0.0 && stats.usage_ratio <= 1.0);
        assert_eq!(stats.current_usage, downloader.buffer.len());
    }

    #[tokio::test]
    async fn test_memory_pressure_aware_operations() {
        let mut downloader = create_test_downloader_with_optimizations().await;
        
        // 测试低内存压力
        let low_pressure = 0.3; // 30%
        // 这应该不会触发立即刷新
        
        // 测试高内存压力
        let high_pressure = 0.9; // 90%
        // 这应该触发立即刷新
        
        // 由于我们没有实际的文件操作，这里主要测试函数不会panic
        let memory_usage = downloader.get_memory_usage_ratio().await;
        assert!(memory_usage >= 0.0 && memory_usage <= 1.0);
    }

    #[tokio::test]
    async fn test_checksum_verification_integration() {
        let downloader = create_test_downloader_with_optimizations().await;
        
        // 创建测试文件
        let test_file_path = "test_verification_file.bin";
        let test_data = b"Test data for verification";
        fs::write(test_file_path, test_data).await.unwrap();
        
        // 测试完整性验证
        let result = downloader.verify_download_integrity(test_file_path).await.unwrap();
        assert!(result); // Should pass since we don't have expected checksums
        
        // 清理
        let _ = fs::remove_file(test_file_path).await;
    }

    #[tokio::test]
    async fn test_multi_algorithm_verification() {
        use super::super::multi_thread_downloader::HttpDownloader;
        use std::collections::HashMap;
        
        let downloader = create_test_downloader_with_optimizations().await;
        
        // 创建测试文件
        let test_file_path = "test_multi_algo_file.bin";
        let test_data = b"Multi-algorithm test data";
        fs::write(test_file_path, test_data).await.unwrap();
        
        // 准备预期的校验和（这里使用空的HashMap，实际使用中应该有真实的校验和）
        let expected_checksums = HashMap::new();
        
        let results = downloader.verify_file_with_multiple_algorithms(test_file_path, &expected_checksums).await.unwrap();
        
        // 由于没有预期的校验和，结果应该为空
        assert!(results.is_empty());
        
        // 清理
        let _ = fs::remove_file(test_file_path).await;
    }
}
