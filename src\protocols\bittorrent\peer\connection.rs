//! BitTorrent对等点连接模块

use std::net::SocketAddr;
use std::time::Duration;

use crate::core::p2p::peer::{Peer, PeerInfo};
use crate::protocols::common::peer::CommonPeer;
use crate::core::p2p::piece::PieceManager;
use std::sync::Arc;
use tokio::sync::Mutex;

/// 对等点连接
pub struct PeerConnection {
    /// 通用连接信息
    pub common: CommonPeer,
    /// 连接超时时间（秒）
    pub timeout_secs: u64,
    /// 分片管理器
    pub piece_manager: Option<Arc<Mutex<dyn PieceManager>>>,
}

impl PeerConnection {
    /// 创建新的对等点连接
    pub fn new(addr: SocketAddr, timeout_secs: u64, piece_manager: Option<Arc<Mutex<dyn PieceManager>>>) -> Self {
        Self {
            common: CommonPeer::new(addr, timeout_secs),
            timeout_secs,
            piece_manager, // 新增字段初始化
        }
    }

    /// 设置分片数量
    pub fn set_num_pieces(&mut self, num_pieces: u32) {
        self.common.num_pieces = num_pieces;
    }

    /// 获取下载速度
    pub fn download_speed(&self) -> u64 {
        self.common.download_speed()
    }

    /// 获取上传量
    pub fn uploaded(&self) -> u64 {
        self.common.uploaded()
    }

    /// 获取连接超时时间
    pub fn timeout(&self) -> Duration {
        Duration::from_secs(self.timeout_secs)
    }
}

/// 对等点连接管理器
use std::collections::HashMap;

pub struct PeerConnectionManager {
    connections: Arc<Mutex<HashMap<SocketAddr, PeerConnection>>>,
}

impl PeerConnectionManager {
    /// 创建新的对等点连接管理器
    pub fn new() -> Self {
        Self {
            connections: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 添加对等点连接
    pub async fn add_peer_connection(&self, peer_connection: PeerConnection) {
        let mut connections = self.connections.lock().await;
        connections.insert(peer_connection.common.peer_info.addr, peer_connection);
    }

    /// 移除对等点连接
    pub async fn remove_peer_connection(&self, addr: SocketAddr) {
        let mut connections = self.connections.lock().await;
        connections.remove(&addr);
    }

    /// 获取对等点连接数量
    pub async fn peer_connection_count(&self) -> usize {
        let connections = self.connections.lock().await;
        connections.len()
    }
}