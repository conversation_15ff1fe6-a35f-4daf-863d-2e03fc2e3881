//! HTTP下载器I/O优化模块
//! 
//! 提供高效的I/O操作，包括内存映射、异步I/O优化、缓冲区管理等

use std::collections::VecDeque;
use std::sync::Arc;
use std::time::{Duration, Instant};
use anyhow::{Result, anyhow};
use tokio::fs::File;
use tokio::io::{AsyncWriteExt, AsyncSeekExt, SeekFrom, BufWriter};
use tokio::sync::{Mutex, RwLock};
use tracing::{debug, info, warn};

/// I/O操作统计
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct IoStats {
    pub total_writes: u64,
    pub total_bytes_written: u64,
    pub total_flushes: u64,
    pub average_write_time: Duration,
    pub peak_write_speed: u64,
    pub buffer_hits: u64,
    pub buffer_misses: u64,
}

impl IoStats {
    pub fn record_write(&mut self, bytes: u64, duration: Duration) {
        self.total_writes += 1;
        self.total_bytes_written += bytes;
        
        // 更新平均写入时间
        let total_time = self.average_write_time.as_nanos() as u64 * (self.total_writes - 1) + duration.as_nanos() as u64;
        self.average_write_time = Duration::from_nanos(total_time / self.total_writes);
        
        // 更新峰值写入速度
        if duration.as_secs_f64() > 0.0 {
            let speed = (bytes as f64 / duration.as_secs_f64()) as u64;
            if speed > self.peak_write_speed {
                self.peak_write_speed = speed;
            }
        }
    }

    pub fn record_flush(&mut self) {
        self.total_flushes += 1;
    }

    pub fn record_buffer_hit(&mut self) {
        self.buffer_hits += 1;
    }

    pub fn record_buffer_miss(&mut self) {
        self.buffer_misses += 1;
    }

    pub fn get_buffer_hit_rate(&self) -> f64 {
        let total = self.buffer_hits + self.buffer_misses;
        if total > 0 {
            self.buffer_hits as f64 / total as f64
        } else {
            0.0
        }
    }
}

/// 智能缓冲区管理器
pub struct SmartBufferManager {
    /// 主缓冲区
    primary_buffer: Vec<u8>,
    /// 备用缓冲区队列
    backup_buffers: VecDeque<Vec<u8>>,
    /// 缓冲区大小
    buffer_size: usize,
    /// 最大缓冲区数量
    max_buffers: usize,
    /// 当前使用的缓冲区数量
    active_buffers: usize,
    /// 自动调整阈值
    auto_resize_threshold: f64,
    /// 统计信息
    stats: IoStats,
}

impl SmartBufferManager {
    pub fn new(initial_buffer_size: usize, max_buffers: usize) -> Self {
        Self {
            primary_buffer: Vec::with_capacity(initial_buffer_size),
            backup_buffers: VecDeque::new(),
            buffer_size: initial_buffer_size,
            max_buffers,
            active_buffers: 1,
            auto_resize_threshold: 0.8, // 80%使用率时触发扩容
            stats: IoStats::default(),
        }
    }

    /// 获取可用缓冲区
    pub fn get_buffer(&mut self) -> Vec<u8> {
        if let Some(buffer) = self.backup_buffers.pop_front() {
            self.stats.record_buffer_hit();
            buffer
        } else {
            self.stats.record_buffer_miss();
            Vec::with_capacity(self.buffer_size)
        }
    }

    /// 归还缓冲区
    pub fn return_buffer(&mut self, mut buffer: Vec<u8>) {
        buffer.clear();
        if self.backup_buffers.len() < self.max_buffers - 1 {
            self.backup_buffers.push_back(buffer);
        }
    }

    /// 自动调整缓冲区大小
    pub fn auto_resize(&mut self, current_speed: u64, target_latency: Duration) {
        let hit_rate = self.stats.get_buffer_hit_rate();
        
        // 如果缓冲区命中率低于阈值，考虑增加缓冲区
        if hit_rate < self.auto_resize_threshold && self.active_buffers < self.max_buffers {
            let new_buffer_size = std::cmp::min(
                self.buffer_size * 2,
                32 * 1024 * 1024 // 最大32MB
            );
            
            if new_buffer_size > self.buffer_size {
                debug!("Increasing buffer size from {} to {} bytes (hit rate: {:.2}%)", 
                       self.buffer_size, new_buffer_size, hit_rate * 100.0);
                self.buffer_size = new_buffer_size;
                self.primary_buffer.reserve(new_buffer_size - self.primary_buffer.capacity());
            }
        }
        
        // 基于速度调整缓冲区大小
        let optimal_buffer_size = self.calculate_optimal_buffer_size(current_speed, target_latency);
        if optimal_buffer_size != self.buffer_size {
            debug!("Adjusting buffer size from {} to {} bytes based on speed", 
                   self.buffer_size, optimal_buffer_size);
            self.buffer_size = optimal_buffer_size;
        }
    }

    /// 计算最优缓冲区大小
    fn calculate_optimal_buffer_size(&self, speed: u64, target_latency: Duration) -> usize {
        // 基于速度和目标延迟计算最优缓冲区大小
        let latency_seconds = target_latency.as_secs_f64();
        let optimal_size = (speed as f64 * latency_seconds) as usize;
        
        // 限制在合理范围内
        std::cmp::max(
            std::cmp::min(optimal_size, 32 * 1024 * 1024), // 最大32MB
            1024 * 1024 // 最小1MB
        )
    }

    pub fn get_stats(&self) -> &IoStats {
        &self.stats
    }
}

/// 优化的文件写入器
pub struct OptimizedFileWriter {
    /// 文件句柄
    file: BufWriter<File>,
    /// 缓冲区管理器
    buffer_manager: Arc<Mutex<SmartBufferManager>>,
    /// 写入统计
    stats: Arc<RwLock<IoStats>>,
    /// 最后刷新时间
    last_flush: Instant,
    /// 刷新间隔
    flush_interval: Duration,
    /// 预写入缓冲区
    write_ahead_buffer: Vec<u8>,
    /// 是否启用预写入
    write_ahead_enabled: bool,
}

impl OptimizedFileWriter {
    pub async fn new(
        file: File, 
        buffer_size: usize, 
        flush_interval: Duration
    ) -> Result<Self> {
        let buf_writer = BufWriter::with_capacity(buffer_size, file);
        let buffer_manager = Arc::new(Mutex::new(
            SmartBufferManager::new(buffer_size, 8) // 最多8个缓冲区
        ));
        
        Ok(Self {
            file: buf_writer,
            buffer_manager,
            stats: Arc::new(RwLock::new(IoStats::default())),
            last_flush: Instant::now(),
            flush_interval,
            write_ahead_buffer: Vec::with_capacity(buffer_size / 4),
            write_ahead_enabled: true,
        })
    }

    /// 优化的写入操作
    pub async fn write_optimized(&mut self, data: &[u8]) -> Result<()> {
        let start_time = Instant::now();
        
        // 如果启用预写入且数据较小，先缓存
        if self.write_ahead_enabled && data.len() < self.write_ahead_buffer.capacity() / 2 {
            if self.write_ahead_buffer.len() + data.len() <= self.write_ahead_buffer.capacity() {
                self.write_ahead_buffer.extend_from_slice(data);
                
                // 检查是否需要刷新预写入缓冲区
                if self.write_ahead_buffer.len() >= self.write_ahead_buffer.capacity() * 3 / 4 {
                    self.flush_write_ahead().await?;
                }
                
                return Ok(());
            } else {
                // 预写入缓冲区空间不足，先刷新
                self.flush_write_ahead().await?;
            }
        }

        // 直接写入
        self.file.write_all(data).await?;
        
        // 记录统计信息
        let duration = start_time.elapsed();
        {
            let mut stats = self.stats.write().await;
            stats.record_write(data.len() as u64, duration);
        }

        // 检查是否需要定期刷新
        if self.last_flush.elapsed() >= self.flush_interval {
            self.flush().await?;
        }

        Ok(())
    }

    /// 刷新预写入缓冲区
    async fn flush_write_ahead(&mut self) -> Result<()> {
        if !self.write_ahead_buffer.is_empty() {
            self.file.write_all(&self.write_ahead_buffer).await?;
            self.write_ahead_buffer.clear();
        }
        Ok(())
    }

    /// 刷新到磁盘
    pub async fn flush(&mut self) -> Result<()> {
        self.flush_write_ahead().await?;
        self.file.flush().await?;
        self.last_flush = Instant::now();
        
        {
            let mut stats = self.stats.write().await;
            stats.record_flush();
        }
        
        Ok(())
    }

    /// 定位到指定位置
    pub async fn seek(&mut self, pos: SeekFrom) -> Result<u64> {
        // 先刷新缓冲区
        self.flush().await?;
        Ok(self.file.seek(pos).await?)
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> IoStats {
        self.stats.read().await.clone()
    }

    /// 启用/禁用预写入
    pub fn set_write_ahead(&mut self, enabled: bool) {
        self.write_ahead_enabled = enabled;
    }

    /// 调整缓冲区大小
    pub async fn adjust_buffer_size(&mut self, new_size: usize) -> Result<()> {
        // 先刷新当前缓冲区
        self.flush().await?;
        
        // 调整预写入缓冲区大小
        self.write_ahead_buffer.reserve(new_size / 4);
        
        // 通知缓冲区管理器
        {
            let mut manager = self.buffer_manager.lock().await;
            manager.auto_resize(0, Duration::from_millis(100)); // 触发自动调整
        }
        
        Ok(())
    }
}

/// 内存使用监控器
pub struct MemoryMonitor {
    /// 最大内存使用量 (字节)
    max_memory_usage: usize,
    /// 当前内存使用量
    current_usage: Arc<RwLock<usize>>,
    /// 内存使用历史
    usage_history: VecDeque<(Instant, usize)>,
    /// 警告阈值
    warning_threshold: f64,
}

impl MemoryMonitor {
    pub fn new(max_memory_mb: usize) -> Self {
        Self {
            max_memory_usage: max_memory_mb * 1024 * 1024,
            current_usage: Arc::new(RwLock::new(0)),
            usage_history: VecDeque::new(),
            warning_threshold: 0.8, // 80%
        }
    }

    /// 记录内存分配
    pub async fn allocate(&mut self, size: usize) -> Result<()> {
        let mut current = self.current_usage.write().await;
        let new_usage = *current + size;
        
        if new_usage > self.max_memory_usage {
            return Err(anyhow!("Memory allocation would exceed limit: {} > {}", 
                              new_usage, self.max_memory_usage));
        }
        
        *current = new_usage;
        self.usage_history.push_back((Instant::now(), new_usage));
        
        // 保持历史记录在合理大小
        while self.usage_history.len() > 1000 {
            self.usage_history.pop_front();
        }
        
        // 检查是否需要警告
        let usage_ratio = new_usage as f64 / self.max_memory_usage as f64;
        if usage_ratio > self.warning_threshold {
            warn!("High memory usage: {:.1}% ({} MB / {} MB)", 
                  usage_ratio * 100.0, 
                  new_usage / 1024 / 1024, 
                  self.max_memory_usage / 1024 / 1024);
        }
        
        Ok(())
    }

    /// 记录内存释放
    pub async fn deallocate(&mut self, size: usize) {
        let mut current = self.current_usage.write().await;
        *current = current.saturating_sub(size);
        self.usage_history.push_back((Instant::now(), *current));
    }

    /// 获取当前内存使用量
    pub async fn get_current_usage(&self) -> usize {
        *self.current_usage.read().await
    }

    /// 获取内存使用率
    pub async fn get_usage_ratio(&self) -> f64 {
        let current = *self.current_usage.read().await;
        current as f64 / self.max_memory_usage as f64
    }

    /// 检查是否可以分配指定大小的内存
    pub async fn can_allocate(&self, size: usize) -> bool {
        let current = *self.current_usage.read().await;
        current + size <= self.max_memory_usage
    }
}
