//! HTTP下载器校验和管理模块
//! 
//! 提供多种哈希算法支持，包括分片级别和整体文件的校验和计算

use std::collections::HashMap;
use std::path::Path;
use anyhow::{Result, anyhow};
use tokio::fs::File;
use tokio::io::{AsyncReadExt, AsyncSeekExt, SeekFrom, BufReader};
use tracing::{debug, info, warn};
use sha2::{Sha256, Digest as Sha2Digest};
use blake3::Hasher as Blake3Hasher;

/// 支持的哈希算法
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash)]
pub enum HashAlgorithm {
    Sha256,
    Blake3,
}

impl HashAlgorithm {
    /// 从字符串解析哈希算法
    pub fn from_str(s: &str) -> Result<Self> {
        match s.to_lowercase().as_str() {
            "sha256" => Ok(HashAlgorithm::Sha256),
            "blake3" => Ok(HashAlgorithm::Blake3),
            _ => Err(anyhow!("Unsupported hash algorithm: {}", s)),
        }
    }

    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            HashAlgorithm::Sha256 => "sha256",
            HashAlgorithm::Blake3 => "blake3",
        }
    }
}

/// 校验和计算器
pub struct ChecksumCalculator {
    /// 缓冲区大小
    buffer_size: usize,
    /// 启用的哈希算法
    enabled_algorithms: Vec<HashAlgorithm>,
}

impl ChecksumCalculator {
    /// 创建新的校验和计算器
    pub fn new(buffer_size: usize) -> Self {
        Self {
            buffer_size,
            enabled_algorithms: vec![HashAlgorithm::Sha256], // 默认使用SHA256
        }
    }

    /// 设置启用的哈希算法
    pub fn with_algorithms(mut self, algorithms: Vec<HashAlgorithm>) -> Self {
        self.enabled_algorithms = algorithms;
        self
    }

    /// 计算文件的校验和
    pub async fn calculate_file_checksums(&self, file_path: &Path) -> Result<HashMap<HashAlgorithm, String>> {
        let file = File::open(file_path).await?;
        let mut reader = BufReader::new(file);
        let mut buffer = vec![0u8; self.buffer_size];
        
        // 初始化所有哈希器
        let mut hashers = self.create_hashers();
        
        // 读取文件并更新所有哈希器
        loop {
            let bytes_read = reader.read(&mut buffer).await?;
            if bytes_read == 0 {
                break;
            }
            
            let data = &buffer[..bytes_read];
            for (algorithm, hasher) in &mut hashers {
                self.update_hasher(hasher, data, *algorithm);
            }
        }
        
        // 完成计算并收集结果
        let mut results = HashMap::new();
        for (algorithm, hasher) in hashers {
            let hash = self.finalize_hasher(hasher, algorithm);
            results.insert(algorithm, hash);
        }
        
        Ok(results)
    }

    /// 计算文件指定范围的校验和
    pub async fn calculate_chunk_checksums(
        &self, 
        file_path: &Path, 
        start: u64, 
        end: u64
    ) -> Result<HashMap<HashAlgorithm, String>> {
        let file = File::open(file_path).await?;
        let mut reader = BufReader::new(file);
        
        // 定位到起始位置
        reader.seek(SeekFrom::Start(start)).await?;
        
        let mut buffer = vec![0u8; self.buffer_size];
        let mut remaining = end - start + 1;
        
        // 初始化所有哈希器
        let mut hashers = self.create_hashers();
        
        // 读取指定范围并更新所有哈希器
        while remaining > 0 {
            let to_read = std::cmp::min(remaining, buffer.len() as u64) as usize;
            let bytes_read = reader.read(&mut buffer[..to_read]).await?;
            
            if bytes_read == 0 {
                break;
            }
            
            let data = &buffer[..bytes_read];
            for (algorithm, hasher) in &mut hashers {
                self.update_hasher(hasher, data, *algorithm);
            }
            
            remaining -= bytes_read as u64;
        }
        
        // 完成计算并收集结果
        let mut results = HashMap::new();
        for (algorithm, hasher) in hashers {
            let hash = self.finalize_hasher(hasher, algorithm);
            results.insert(algorithm, hash);
        }
        
        Ok(results)
    }

    /// 验证文件校验和
    pub async fn verify_file_checksums(
        &self,
        file_path: &Path,
        expected_checksums: &HashMap<HashAlgorithm, String>
    ) -> Result<HashMap<HashAlgorithm, bool>> {
        let calculated_checksums = self.calculate_file_checksums(file_path).await?;
        let mut results = HashMap::new();
        
        for (algorithm, expected) in expected_checksums {
            if let Some(calculated) = calculated_checksums.get(algorithm) {
                let matches = calculated.eq_ignore_ascii_case(expected);
                results.insert(*algorithm, matches);
                
                if matches {
                    debug!("Checksum verification passed for {}: {}", algorithm.as_str(), calculated);
                } else {
                    warn!("Checksum verification failed for {}: expected {}, got {}", 
                          algorithm.as_str(), expected, calculated);
                }
            } else {
                warn!("No calculated checksum found for algorithm: {}", algorithm.as_str());
                results.insert(*algorithm, false);
            }
        }
        
        Ok(results)
    }

    /// 创建哈希器
    fn create_hashers(&self) -> Vec<(HashAlgorithm, Box<dyn HasherTrait>)> {
        let mut hashers = Vec::new();
        
        for &algorithm in &self.enabled_algorithms {
            let hasher: Box<dyn HasherTrait> = match algorithm {
                HashAlgorithm::Sha256 => Box::new(Sha256::new()),
                HashAlgorithm::Blake3 => Box::new(Blake3Hasher::new()),
            };
            hashers.push((algorithm, hasher));
        }
        
        hashers
    }

    /// 更新哈希器
    fn update_hasher(&self, hasher: &mut Box<dyn HasherTrait>, data: &[u8], algorithm: HashAlgorithm) {
        match algorithm {
            HashAlgorithm::Sha256 => {
                if let Some(sha256_hasher) = hasher.as_any_mut().downcast_mut::<Sha256>() {
                    sha256_hasher.update(data);
                }
            },
            HashAlgorithm::Blake3 => {
                if let Some(blake3_hasher) = hasher.as_any_mut().downcast_mut::<Blake3Hasher>() {
                    blake3_hasher.update(data);
                }
            },
        }
    }

    /// 完成哈希计算
    fn finalize_hasher(&self, hasher: Box<dyn HasherTrait>, algorithm: HashAlgorithm) -> String {
        match algorithm {
            HashAlgorithm::Sha256 => {
                if let Some(sha256_hasher) = hasher.as_any().downcast_ref::<Sha256>() {
                    format!("{:x}", sha256_hasher.clone().finalize())
                } else {
                    String::new()
                }
            },
            HashAlgorithm::Blake3 => {
                if let Some(blake3_hasher) = hasher.as_any().downcast_ref::<Blake3Hasher>() {
                    blake3_hasher.clone().finalize().to_hex().to_string()
                } else {
                    String::new()
                }
            },
        }
    }
}

/// 哈希器特征，用于类型擦除
trait HasherTrait: Send + Sync {
    fn as_any(&self) -> &dyn std::any::Any;
    fn as_any_mut(&mut self) -> &mut dyn std::any::Any;
}


impl HasherTrait for Sha256 {
    fn as_any(&self) -> &dyn std::any::Any { self }
    fn as_any_mut(&mut self) -> &mut dyn std::any::Any { self }
}

impl HasherTrait for Blake3Hasher {
    fn as_any(&self) -> &dyn std::any::Any { self }
    fn as_any_mut(&mut self) -> &mut dyn std::any::Any { self }
}

/// 分片校验和信息
#[derive(Debug, Clone)]
pub struct ChunkChecksum {
    pub index: usize,
    pub start: u64,
    pub end: u64,
    pub checksums: HashMap<HashAlgorithm, String>,
}

impl ChunkChecksum {
    pub fn new(index: usize, start: u64, end: u64) -> Self {
        Self {
            index,
            start,
            end,
            checksums: HashMap::new(),
        }
    }

    pub fn add_checksum(&mut self, algorithm: HashAlgorithm, checksum: String) {
        self.checksums.insert(algorithm, checksum);
    }

    pub fn get_checksum(&self, algorithm: HashAlgorithm) -> Option<&String> {
        self.checksums.get(&algorithm)
    }
}
