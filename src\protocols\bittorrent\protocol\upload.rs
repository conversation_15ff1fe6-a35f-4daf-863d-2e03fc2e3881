use anyhow::{Result, anyhow};
use tracing::{debug, info, warn};

use crate::protocols::bittorrent::message::BitTorrentMessage;
use crate::protocols::bittorrent::manager::peer_manager_trait::PeerManagerTrait;
use super::core::BitTorrentProtocol;
use crate::core::p2p::peer::Peer;

impl BitTorrentProtocol {
    /// 处理上传任务
    pub(crate) async fn process_uploads(&mut self) -> Result<()> {
        // 检查是否已初始化
        if !self.initialized {
            return Err(anyhow!("BitTorrent protocol not initialized"));
        }

        // 检查是否启用上传
        if !self.upload_enabled {
            return Ok(());
        }

        // 检查是否有分片管理器和对等点管理器
        if self.piece_manager.is_none() || self.peer_manager.is_none() {
            return Ok(());
        }

        // 获取分片管理器和对等点管理器
        let piece_manager = self.piece_manager.as_ref().unwrap().clone();
        let peer_manager = self.peer_manager.as_mut().unwrap();

        // 检查可上传的分片数量
        let available_pieces = {
            let piece_manager_guard = piece_manager.lock().await;
            let total_pieces = piece_manager_guard.all_piece_states().await?.len() as u32;
            let mut verified_pieces = 0;
            
            for i in 0..total_pieces {
                if piece_manager_guard.piece_state(i).await? == crate::core::p2p::piece::PieceState::Verified {
                    verified_pieces += 1;
                }
            }
            
            debug!("Available pieces for upload: {}/{}", verified_pieces, total_pieces);
            verified_pieces
        };

        // 处理所有对等点的上传请求
        let peers = peer_manager.get_peers().await?;
        let mut total_uploaded = 0u64;

        for (addr, peer_arc) in &peers {
            let mut peer = peer_arc.lock().await;

            // 如果对等点已连接，处理上传请求
            if peer.is_connected() {
                // 处理上传请求
                if let Err(e) = peer.process_uploads().await {
                    warn!("Failed to process uploads for peer {}: {}", addr, e);
                    continue;
                }

                // 更新上传统计
                total_uploaded += peer.uploaded();
            }
        }

        // 更新总上传量
        if total_uploaded > self.uploaded {
            let new_uploaded = total_uploaded - self.uploaded;
            self.uploaded = total_uploaded;

            // 如果有带宽调度器，更新上传统计
            if let Some(scheduler) = &self.bandwidth_scheduler {
                if let Err(e) = scheduler.update_upload_stats(self.task_id, new_uploaded).await {
                    warn!("Failed to update upload stats: {}", e);
                }
            }

            // 记录上传统计
            debug!("Uploaded {} bytes, total: {} bytes", new_uploaded, self.uploaded);
        }

        // 如果启用了上传速率限制，应用限制
        if let Some(scheduler) = &self.bandwidth_scheduler {
            if let Ok(Some(upload_limit)) = scheduler.get_task_upload_limit(self.task_id).await {
                let peers_count = peers.len() as u64;
                let per_peer_limit = if peers_count > 0 {
                    upload_limit / peers_count
                } else {
                    upload_limit
                };

                for (_addr, peer_arc) in &peers {
                    let mut peer = peer_arc.lock().await;
                    if peer.is_connected() {
                        peer.set_upload_rate_limit(Some(per_peer_limit));
                    }
                }
            }
        }

        Ok(())
    }

    /// 设置上传状态
    pub async fn set_upload_enabled(&mut self, enabled: bool) -> Result<()> {
        self.upload_enabled = enabled;

        // 如果有对等点管理器，更新所有对等点的上传状态
        if let Some(peer_manager) = &mut self.peer_manager {
            let peers = peer_manager.get_peers().await?;

            for (_addr, peer_arc) in &peers {
                let mut peer = peer_arc.lock().await;
                if peer.is_connected() {
                    // 如果启用上传，解除阻塞；否则阻塞
                    if enabled {
                        // 只有当对方对我们感兴趣时才解除阻塞
                        if peer.upload_manager.peer_interested {
                            if let Err(e) = peer.send_bt_message(BitTorrentMessage::Unchoke).await {
                                warn!("Failed to send unchoke message: {}", e);
                            } else {
                                debug!("Sent unchoke message to peer: {}", peer.connection.common.peer_info.addr);
                            }
                        }
                    } else {
                        // 阻塞所有对等点
                        if let Err(e) = peer.send_bt_message(BitTorrentMessage::Choke).await {
                            warn!("Failed to send choke message: {}", e);
                        } else {
                            debug!("Sent choke message to peer: {}", peer.connection.common.peer_info.addr);
                        }
                    }
                }
            }
        }

        info!("Upload {} for task {}", if enabled { "enabled" } else { "disabled" }, self.task_id);
        Ok(())
    }

    /// 设置上传速率限制
    pub async fn set_upload_rate_limit(&mut self, bytes_per_second: Option<u64>) -> Result<()> {
        // 如果有带宽调度器，设置上传速率限制
        if let Some(scheduler) = &self.bandwidth_scheduler {
            scheduler.set_task_upload_limit(self.task_id, bytes_per_second).await?;
        }

        // 如果有对等点管理器，更新所有对等点的上传速率限制
        if let Some(peer_manager) = &mut self.peer_manager {
            let peers = peer_manager.get_peers().await?;
            let peers_count = peers.len() as u64;

            // 计算每个对等点的上传速率限制
            let per_peer_limit = if let Some(limit) = bytes_per_second {
                if peers_count > 0 {
                    Some(limit / peers_count)
                } else {
                    Some(limit)
                }
            } else {
                None
            };

            for (_addr, peer_arc) in &peers {
                let mut peer = peer_arc.lock().await;
                if peer.is_connected() {
                    peer.upload_manager.set_upload_rate_limit(per_peer_limit);
                }
            }
        }

        info!("Upload rate limit set to {:?} bytes/second for task {}", bytes_per_second, self.task_id);
        Ok(())
    }
}
