//! HTTP download protocol implementation

mod protocol;
mod downloader;
mod core;
mod utils;

// 各个功能模块 - 声明为公共模块以便重新导出
pub mod file_utils;
pub mod buffer_manager;
pub mod http_client;
pub mod resume_manager;
pub mod speed_tracker;
pub mod cancellation;
pub mod chunk_manager;
pub mod multi_thread_downloader;
pub mod metadata_manager;
pub mod performance_optimizer;
pub mod checksum_manager;
pub mod io_optimizer;

#[cfg(test)]
mod tests;

// 重新导出公共接口
pub use downloader::HttpDownloader;

// 导出内部模块，供其他模块使用
pub(crate) mod internal {
    pub use super::downloader::*;
    pub use super::core::*;
    pub use super::utils::*;
    pub use super::file_utils::*;
    pub use super::buffer_manager::*;
    pub use super::http_client::*;
    pub use super::resume_manager::*;
    pub use super::speed_tracker::*;
    pub use super::cancellation::*;
    pub use super::chunk_manager::*;
    pub use super::multi_thread_downloader::*;
    pub use super::metadata_manager::*;
    pub use super::performance_optimizer::*;
}

// 导出公共内部工具
pub use utils::*;
