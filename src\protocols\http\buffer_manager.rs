//! HTTP下载器缓冲区管理模块

use std::time::Instant;
use std::collections::VecDeque;
use anyhow::Result;
use tokio::io::AsyncWriteExt;
use tracing::{debug, info, warn};

use super::downloader::HttpDownloader;

impl HttpDownloader {
    /// 刷新缓冲区到文件
    pub(crate) async fn flush_buffer(&mut self, file: &mut tokio::fs::File) -> Result<()> {
        if !self.buffer.is_empty() {
            debug!("Flushing {} bytes from buffer to file", self.buffer.len());
            file.write_all(&self.buffer).await?;
            file.flush().await?;
            self.buffer.clear();
            self.last_flush_time = Some(Instant::now());
        }
        Ok(())
    }
    
    /// 写入数据到缓冲区，如果缓冲区满或达到刷新间隔则刷新到文件
    pub(crate) async fn write_to_buffer(&mut self, data: &[u8], file: &mut tokio::fs::File) -> Result<()> {
        // 如果数据加上当前缓冲区大小超过了缓冲区容量，先刷新缓冲区
        if self.buffer.len() + data.len() > self.buffer_size {
            self.flush_buffer(file).await?
        }
        
        // 如果单个数据块大于缓冲区容量，直接写入文件
        if data.len() > self.buffer_size {
            debug!("Data chunk size ({}) exceeds buffer size, writing directly to file", data.len());
            file.write_all(data).await?;
            file.flush().await?;
            self.last_flush_time = Some(Instant::now());
        } else {
            // 否则添加到缓冲区
            self.buffer.extend_from_slice(data);
            
            // 检查是否需要定期刷新
            let should_flush = if let Some(last_flush) = self.last_flush_time {
                Instant::now().duration_since(last_flush) >= self.flush_interval
            } else {
                true
            };
            
            if should_flush {
                self.flush_buffer(file).await?
            }
        }
        
        Ok(())
    }

    /// 智能缓冲区管理 - 基于网络状况动态调整
    pub(crate) async fn smart_buffer_management(&mut self, current_speed: u64, network_latency: std::time::Duration) -> Result<()> {
        // 基于当前下载速度计算最优缓冲区大小
        let optimal_buffer_size = self.calculate_optimal_buffer_size(current_speed, network_latency);

        if optimal_buffer_size != self.buffer_size {
            debug!("Adjusting buffer size from {} to {} bytes based on network conditions",
                   self.buffer_size, optimal_buffer_size);

            // 先刷新当前缓冲区
            if let Ok(mut file) = tokio::fs::OpenOptions::new().write(true).open(&self.output_path).await {
                self.flush_buffer(&mut file).await?;
            }

            // 调整缓冲区大小
            self.buffer_size = optimal_buffer_size;
            self.buffer = Vec::with_capacity(optimal_buffer_size);
        }

        Ok(())
    }

    /// 计算最优缓冲区大小
    fn calculate_optimal_buffer_size(&self, speed: u64, latency: std::time::Duration) -> usize {
        // 基于带宽延迟积(BDP)计算最优缓冲区大小
        let bdp = (speed as f64 * latency.as_secs_f64()) as usize;

        // 应用启发式规则
        let optimal_size = if speed > 100 * 1024 * 1024 { // > 100MB/s
            std::cmp::max(bdp, 32 * 1024 * 1024) // 至少32MB
        } else if speed > 10 * 1024 * 1024 { // > 10MB/s
            std::cmp::max(bdp, 16 * 1024 * 1024) // 至少16MB
        } else if speed > 1024 * 1024 { // > 1MB/s
            std::cmp::max(bdp, 8 * 1024 * 1024) // 至少8MB
        } else {
            std::cmp::max(bdp, 4 * 1024 * 1024) // 至少4MB
        };

        // 限制最大缓冲区大小以避免内存过度使用
        std::cmp::min(optimal_size, 64 * 1024 * 1024) // 最大64MB
    }

    /// 预分配缓冲区池
    pub(crate) fn preallocate_buffer_pool(&mut self, pool_size: usize) -> VecDeque<Vec<u8>> {
        let mut pool = VecDeque::with_capacity(pool_size);

        for _ in 0..pool_size {
            let buffer = Vec::with_capacity(self.buffer_size);
            pool.push_back(buffer);
        }

        info!("Preallocated buffer pool with {} buffers of {} bytes each",
              pool_size, self.buffer_size);

        pool
    }

    /// 内存压力感知的缓冲区管理
    pub(crate) async fn memory_pressure_aware_flush(&mut self, file: &mut tokio::fs::File, memory_usage_ratio: f64) -> Result<()> {
        // 如果内存使用率过高，立即刷新
        if memory_usage_ratio > 0.8 {
            warn!("High memory pressure detected ({:.1}%), forcing buffer flush", memory_usage_ratio * 100.0);
            self.flush_buffer(file).await?;
        } else if memory_usage_ratio > 0.6 {
            // 中等内存压力，缩短刷新间隔
            let reduced_interval = self.flush_interval / 2;
            if let Some(last_flush) = self.last_flush_time {
                if Instant::now().duration_since(last_flush) >= reduced_interval {
                    debug!("Memory pressure detected ({:.1}%), reducing flush interval", memory_usage_ratio * 100.0);
                    self.flush_buffer(file).await?;
                }
            }
        }

        Ok(())
    }

    /// 获取缓冲区使用统计
    pub fn get_buffer_stats(&self) -> BufferStats {
        BufferStats {
            buffer_size: self.buffer_size,
            current_usage: self.buffer.len(),
            usage_ratio: self.buffer.len() as f64 / self.buffer_size as f64,
            last_flush: self.last_flush_time,
        }
    }
}

/// 缓冲区统计信息
#[derive(Debug, Clone)]
pub struct BufferStats {
    pub buffer_size: usize,
    pub current_usage: usize,
    pub usage_ratio: f64,
    pub last_flush: Option<Instant>,
}

impl BufferStats {
    pub fn is_nearly_full(&self) -> bool {
        self.usage_ratio > 0.8
    }

    pub fn needs_flush(&self, flush_interval: std::time::Duration) -> bool {
        if let Some(last_flush) = self.last_flush {
            Instant::now().duration_since(last_flush) >= flush_interval
        } else {
            true
        }
    }
}
